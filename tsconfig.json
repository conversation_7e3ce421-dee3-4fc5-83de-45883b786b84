{
  // "references": [
  //   { "path": "./tsconfig.app.json" },
  //   { "path": "./tsconfig.node.json" }
  // ],
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    },
    "esModuleInterop": true,
    "module": "esnext",
    "target": "esnext",
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "useDefineForClassFields": true,
    "resolveJsonModule": true,
    "moduleResolution": "node",
  },
  // "include": ["src", "types/**/*.d.ts"]
  "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.ts","types/**/*.d.ts", "auto-imports.d.ts"]
}
