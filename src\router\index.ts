// src/router/index.ts

import { createRouter, createWebHashHistory, RouteRecordRaw } from "vue-router";
import type { App } from "vue";

const routes: Array<RouteRecordRaw> = [
    {
        path: '/',
        name: 'Home',
        component: () => import('@/views/Home.vue'),
    },

];

const router = createRouter({
    history: createWebHashHistory(),
    routes,
});

// 在这里添加路由的导航守卫
router.beforeEach((to, from, next) => {
    // console.log('Navigating to:', to.path);
    next();
});

export const setupRouter = (app: App<Element>) => {
    app.use(router);
};

export default router;
