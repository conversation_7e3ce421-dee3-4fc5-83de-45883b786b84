<template>
  <div class="robot-container">
    <!-- 控制面板 -->
    <div class="control-panel">
      <h3>机械臂关节控制</h3>
      <div v-for="(joint, index) in joints" :key="index" class="joint-control">
        <label>关节 {{ index + 1 }}: {{ joint.angle.toFixed(1) }}°</label>
        <input
          type="range"
          :min="joint.min"
          :max="joint.max"
          :step="1"
          v-model="joint.angle"
          @input="updateJoint(index, joint.angle)"
          class="slider"
        />
      </div>
      <button @click="resetPose" class="reset-btn">重置姿态</button>
    </div>

    <!-- 3D 视图 -->
    <div id="robot-viewer" class="robot-viewer"></div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import * as THREE from "three";
import { OrbitControls } from "three/addons/controls/OrbitControls.js";

// 机械臂关节配置
const joints = ref([
  { angle: 0, min: -180, max: 180 }, // 基座旋转
  { angle: 0, min: -90, max: 90 }, // 肩部
  { angle: 0, min: -135, max: 135 }, // 肘部
  { angle: 0, min: -180, max: 180 }, // 腕部旋转
  { angle: 0, min: -90, max: 90 }, // 腕部俯仰
  { angle: 0, min: -180, max: 180 }, // 末端执行器
]);

let scene, camera, renderer, controls;
let robotParts = [];
let robotGroup;

// 初始化 Three.js 场景
function initScene() {
  // 创建场景
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0xf0f0f0);

  // 创建相机
  camera = new THREE.PerspectiveCamera(
    75,
    window.innerWidth / window.innerHeight,
    0.1,
    1000
  );
  camera.position.set(5, 5, 5);
  camera.lookAt(0, 0, 0);

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(window.innerWidth, window.innerHeight);
  renderer.shadowMap.enabled = true;
  renderer.shadowMap.type = THREE.PCFSoftShadowMap;

  // 添加轨道控制器
  controls = new OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true;
  controls.dampingFactor = 0.05;

  // 添加光源
  addLights();

  // 添加地面
  addGround();

  // 创建机械臂
  createRobot();
}

// 添加光源
function addLights() {
  // 环境光
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
  scene.add(ambientLight);

  // 方向光
  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(10, 10, 5);
  directionalLight.castShadow = true;
  directionalLight.shadow.mapSize.width = 2048;
  directionalLight.shadow.mapSize.height = 2048;
  scene.add(directionalLight);
}

// 添加地面
function addGround() {
  const groundGeometry = new THREE.PlaneGeometry(20, 20);
  const groundMaterial = new THREE.MeshLambertMaterial({ color: 0xcccccc });
  const ground = new THREE.Mesh(groundGeometry, groundMaterial);
  ground.rotation.x = -Math.PI / 2;
  ground.receiveShadow = true;
  scene.add(ground);

  // 添加网格
  const gridHelper = new THREE.GridHelper(20, 20);
  scene.add(gridHelper);
}

// 创建机械臂
function createRobot() {
  robotGroup = new THREE.Group();
  robotParts = [];

  // 机械臂各部分的配置
  const partConfigs = [
    {
      width: 0.8,
      height: 0.3,
      depth: 0.8,
      color: 0x4a90e2,
      position: [0, 0.15, 0],
    }, // 基座
    {
      width: 0.3,
      height: 1.5,
      depth: 0.3,
      color: 0xe74c3c,
      position: [0, 0.75, 0],
    }, // 肩部
    {
      width: 0.25,
      height: 1.2,
      depth: 0.25,
      color: 0x2ecc71,
      position: [0, 0.6, 0],
    }, // 上臂
    {
      width: 0.2,
      height: 1.0,
      depth: 0.2,
      color: 0xf39c12,
      position: [0, 0.5, 0],
    }, // 前臂
    {
      width: 0.15,
      height: 0.6,
      depth: 0.15,
      color: 0x9b59b6,
      position: [0, 0.3, 0],
    }, // 腕部
    {
      width: 0.1,
      height: 0.4,
      depth: 0.1,
      color: 0x34495e,
      position: [0, 0.2, 0],
    }, // 末端执行器
  ];

  // 创建各个关节部分
  partConfigs.forEach((config, index) => {
    const geometry = new THREE.BoxGeometry(
      config.width,
      config.height,
      config.depth
    );
    const material = new THREE.MeshPhongMaterial({ color: config.color });
    const part = new THREE.Mesh(geometry, material);

    part.position.set(...config.position);
    part.castShadow = true;
    part.receiveShadow = true;

    // 创建关节组
    const jointGroup = new THREE.Group();
    jointGroup.add(part);

    if (index === 0) {
      // 基座直接添加到机械臂组
      robotGroup.add(jointGroup);
    } else {
      // 其他部分添加到前一个关节
      const prevHeight = partConfigs[index - 1].height;
      jointGroup.position.y = prevHeight;
      robotParts[index - 1].add(jointGroup);
    }

    robotParts.push(jointGroup);
  });

  scene.add(robotGroup);
}

// 更新关节角度
function updateJoint(jointIndex, angle) {
  if (robotParts[jointIndex]) {
    const radians = (angle * Math.PI) / 180;

    switch (jointIndex) {
      case 0: // 基座旋转
        robotParts[jointIndex].rotation.y = radians;
        break;
      case 1: // 肩部
        robotParts[jointIndex].rotation.z = radians;
        break;
      case 2: // 肘部
        robotParts[jointIndex].rotation.z = radians;
        break;
      case 3: // 腕部旋转
        robotParts[jointIndex].rotation.y = radians;
        break;
      case 4: // 腕部俯仰
        robotParts[jointIndex].rotation.z = radians;
        break;
      case 5: // 末端执行器
        robotParts[jointIndex].rotation.y = radians;
        break;
    }
  }
}

// 重置机械臂姿态
function resetPose() {
  joints.value.forEach((joint, index) => {
    joint.angle = 0;
    updateJoint(index, 0);
  });
}

// 渲染循环
function animate() {
  requestAnimationFrame(animate);
  controls.update();
  renderer.render(scene, camera);
}

// 处理窗口大小变化
function handleResize() {
  const container = document.getElementById("robot-viewer");
  if (container) {
    const width = container.clientWidth;
    const height = container.clientHeight;

    camera.aspect = width / height;
    camera.updateProjectionMatrix();
    renderer.setSize(width, height);
  }
}

onMounted(() => {
  initScene();

  const container = document.getElementById("robot-viewer");
  if (container) {
    container.appendChild(renderer.domElement);
    handleResize();
  }

  animate();

  window.addEventListener("resize", handleResize);
});
</script>

<style scoped>
.robot-container {
  display: flex;
  height: 100vh;
  font-family: Arial, sans-serif;
}

.control-panel {
  width: 300px;
  padding: 20px;
  background-color: #f8f9fa;
  border-right: 1px solid #dee2e6;
  overflow-y: auto;
}

.control-panel h3 {
  margin-top: 0;
  color: #333;
  text-align: center;
}

.joint-control {
  margin-bottom: 20px;
}

.joint-control label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #555;
}

.slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #ddd;
  outline: none;
  -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
  border: none;
}

.reset-btn {
  width: 100%;
  padding: 10px;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  margin-top: 20px;
}

.reset-btn:hover {
  background-color: #c0392b;
}

.robot-viewer {
  flex: 1;
  position: relative;
}

.robot-viewer canvas {
  display: block;
}
</style>
