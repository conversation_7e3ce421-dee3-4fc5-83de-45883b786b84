<!-- 用于3D预览机械臂的页面，会有左侧边栏用于实时控制机械臂（可收缩,element-ui折叠面板），
    中间是3D模型（使用three.js实现），右侧是机械臂反馈情况（可收缩，element-ui折叠面板） -->
<template>
  <div class="common-layout">
    <el-container>
      <!-- <el-drawer v-model="drawer" direction="ltr" size="20%"> -->
      <el-aside>
        <Menu @sliderInput="sliderInput" />
      </el-aside>
      <!-- </el-drawer> -->
      <!-- <div class="btn" v-show="!drawer"> -->
      <!-- <el-button type="primary" :icon="Operation" circle size="large" @click="drawerSwitch" /> -->
      <!-- </div> -->
      <div id="webgl"></div>
    </el-container>
  </div>
</template>

<script setup>
import { LoadingManager, MathUtils } from "three";
import { Operation } from "@element-plus/icons-vue";
import { ref } from "vue";
import Menu from "@/components/Menu.vue";
import Menu2 from "../views/test2.vue";
import * as THREE from "three";
// 控制器
import { OrbitControls } from "three/addons/controls/OrbitControls.js";
//URDF模型解析
//import URDFLoader from 'three/addons/loaders/URDFLoader.js';
import URDFLoader from "urdf-loader";
import { onMounted } from "vue";

// 创建场景->相机->渲染器->相机添加到场景中->渲染器渲染场景和相机->渲染器添加到dom中
var robot;
let scene = "";
let camera = "";
let renderer = "";
// 轨道控制器
let controls = "";
let handList = [];
let circlePosition = "";
const drawer = ref(false);
var canvas = ref(null);

// 设置各个关节的角度
function sliderInput(value, name) {
  // 找到要设置的关节
  //let target = handList.find(item => item.materialLibraries.join('') === name + '.mtl');
  //target.rotation[direction] = value;
  //console.log("此时的ROBOT,value,name",robot,value,name);
  name = "link" + String(name) + "_joint";
  robot.joints[name].setJointValue(MathUtils.degToRad(value));
  //robot.joints[name].setJointValue(value);
  //robot.updateMatrixWorld(true);
}

// 开关侧边栏控制栏
const drawerSwitch = () => {
  drawer.value = !drawer.value;
};

// 初始化
function initBase() {
  scene = new THREE.Scene();
  scene.position.set(0, 0.2, 0.8);
  camera = new THREE.PerspectiveCamera(
    45,
    window.innerWidth / window.innerHeight,
    0.1,
    100
  );
  camera.position.set(1.86, 1.51, 1.02);
  //camera.position.set(0, 0, 0);
  //camera.lookAt(scene.position);

  // 相机添加到场景中
  scene.add(camera);

  // antialias:开启抗锯齿  logarithmicDepthBuffer:使用对数深度缓冲器,一般在单个场景处理较大的差异
  renderer = new THREE.WebGLRenderer({
    antialias: true,
    logarithmicDepthBuffer: true,
  });
  renderer.setSize(window.innerWidth, window.innerHeight);
  renderer.setClearColor("#DCDCDC");
}

// 添加光线
function addLight() {
  const positions = [
    { x: 10, y: 10, z: 10 },
    { x: -10, y: 10, z: -10 },
    { x: -30, y: 10, z: 0 },
    { x: 0, y: -10, z: 0 },
  ];
  positions.forEach((pos) => {
    const light = new THREE.DirectionalLight("#8fbad3", 1);
    light.position.set(pos.x, pos.y, pos.z);
    //将灯光添加到场景中
    scene.add(light);
  });
}

// 轨道控制器
function initOrbitControls() {
  controls = new OrbitControls(camera, renderer.domElement);
  // 开启阻尼 更加真实
  controls.enableDamping = true;
}

// render渲染器
function render() {
  // 渲染器更新
  renderer.render(scene, camera);
  // 控制器更新
  controls.update();
  requestAnimationFrame(render);
}

// 辅助线
function addHelpLine() {
  //坐标轴辅助显示
  const arrowHelper = new THREE.AxesHelper(5);
  scene.add(arrowHelper);

  const gridHelper = new THREE.GridHelper(100, 20);
  scene.add(gridHelper);
}

// 初始化
initBase();
// 添加灯光
addLight();
// 添加控制器
initOrbitControls();
// 添加辅助线和网格地板
addHelpLine();

function initRobot() {
  // 导入Robot模型
  const manager = new LoadingManager();
  const loader = new URDFLoader(manager);
  loader.load("./lineRobot_20240229/urdf/lineRobot_20240229.urdf", (result) => {
    robot = result;
    //console.log("ROBOT:",robot);
    //设置ROBOT坐标z朝上
    robot.rotation.x = Math.PI / 2;
    robot.rotation.x *= -1;
    //设置ROBOT在坐标原点
    robot.position.x = 0;
    robot.position.y = 0;
    robot.position.z = 0;
    scene.add(robot);
  });
}
initRobot();

onMounted(() => {
  // 将渲染器添加到页面中
  //document.body.appendChild(renderer.domElement);
  document.getElementById("webgl").appendChild(renderer.domElement);
  render();
  // 窗口大小处理
  window.addEventListener("resize", () => {
    // 更新相机宽高比
    camera.aspect = window.innerWidth / window.innerHeight;
    // 更新相机的投影矩阵
    camera.updateProjectionMatrix();
    // 更新渲染器渲染的尺寸大小
    renderer.setSize(window.innerWidth, window.innerHeight);
    // 设置渲染器的像素比(window.devicePixelRatio:当前设备的像素比)
    renderer.setPixelRatio(window.innerWidth / window.innerHeight);

    // 获取相机位置
    // const cameraPosition = camera.position;
    // console.log('相机位置：', cameraPosition);
  });
});
</script>

<style>
.btn {
  position: fixed;
  bottom: 5%;
  left: 50%;
  transform: translateX(-50%);
}
</style>
