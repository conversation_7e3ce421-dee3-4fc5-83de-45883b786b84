<template>
  <div class="robot-container">
    <!-- 控制面板 -->
    <div class="control-panel">
      <h3>机械臂关节控制</h3>
      <div v-for="(joint, index) in joints" :key="index" class="joint-control">
        <label>关节 {{ index + 1 }}: {{ joint.angle.toFixed(1) }}°</label>
        <input
          type="range"
          :min="joint.min"
          :max="joint.max"
          :step="1"
          v-model="joint.angle"
          @input="updateJoint(index, joint.angle)"
          class="slider"
        />
      </div>
      <button @click="resetPose" class="reset-btn">重置姿态</button>
    </div>

    <!-- 3D 视图 -->
    <div id="robot-viewer" class="robot-viewer"></div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import * as THREE from "three";
import { OrbitControls } from "three/addons/controls/OrbitControls.js";

// 机械臂关节配置
const joints = ref([
  { angle: 0, min: -180, max: 180 }, // 基座旋转
  { angle: 0, min: -90, max: 90 }, // 肩部
  { angle: 0, min: -135, max: 135 }, // 肘部
  { angle: 0, min: -180, max: 180 }, // 腕部旋转
  { angle: 0, min: -90, max: 90 }, // 腕部俯仰
  { angle: 0, min: -180, max: 180 }, // 末端执行器
]);

let scene, camera, renderer, controls;
let robotParts = [];
let robotGroup;

// 初始化 Three.js 场景
function initScene() {
  // 创建场景
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0xf0f0f0);

  // 创建相机
  camera = new THREE.PerspectiveCamera(
    75,
    window.innerWidth / window.innerHeight,
    0.1,
    1000
  );
  camera.position.set(5, 5, 5);
  camera.lookAt(0, 0, 0);

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(window.innerWidth, window.innerHeight);
  renderer.shadowMap.enabled = true;
  renderer.shadowMap.type = THREE.PCFSoftShadowMap;

  // 添加轨道控制器
  controls = new OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true;
  controls.dampingFactor = 0.05;

  // 添加光源
  addLights();

  // 添加地面
  addGround();

  // 创建机械臂
  createRobot();
}

// 添加光源
function addLights() {
  // 环境光
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
  scene.add(ambientLight);

  // 方向光
  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(10, 10, 5);
  directionalLight.castShadow = true;
  directionalLight.shadow.mapSize.width = 2048;
  directionalLight.shadow.mapSize.height = 2048;
  scene.add(directionalLight);
}

// 添加地面
function addGround() {
  const groundGeometry = new THREE.PlaneGeometry(20, 20);
  const groundMaterial = new THREE.MeshLambertMaterial({ color: 0xcccccc });
  const ground = new THREE.Mesh(groundGeometry, groundMaterial);
  ground.rotation.x = -Math.PI / 2;
  ground.receiveShadow = true;
  scene.add(ground);

  // 添加网格
  const gridHelper = new THREE.GridHelper(20, 20);
  scene.add(gridHelper);
}

// 创建机械臂
function createRobot() {
  robotGroup = new THREE.Group();
  robotParts = [];

  // 创建基座
  createBase();

  // 创建肩部关节
  createShoulderJoint();

  // 创建上臂
  createUpperArm();

  // 创建肘部关节
  createElbowJoint();

  // 创建前臂
  createForearm();

  // 创建腕部
  createWrist();

  scene.add(robotGroup);
}

// 创建基座
function createBase() {
  const baseGroup = new THREE.Group();

  // 基座底盘
  const baseGeometry = new THREE.CylinderGeometry(0.6, 0.8, 0.3, 16);
  const baseMaterial = new THREE.MeshPhongMaterial({ color: 0x2c3e50 });
  const base = new THREE.Mesh(baseGeometry, baseMaterial);
  base.position.y = 0.15;
  base.castShadow = true;
  base.receiveShadow = true;

  baseGroup.add(base);
  robotGroup.add(baseGroup);
  robotParts.push(baseGroup);
}

// 创建肩部关节
function createShoulderJoint() {
  const shoulderGroup = new THREE.Group();

  // 肩部支柱
  const pillarGeometry = new THREE.CylinderGeometry(0.15, 0.15, 1.0, 12);
  const pillarMaterial = new THREE.MeshPhongMaterial({ color: 0x3498db });
  const pillar = new THREE.Mesh(pillarGeometry, pillarMaterial);
  pillar.position.y = 0.5;
  pillar.castShadow = true;

  // 肩部关节球
  const jointGeometry = new THREE.SphereGeometry(0.2, 16, 16);
  const jointMaterial = new THREE.MeshPhongMaterial({ color: 0xe74c3c });
  const joint = new THREE.Mesh(jointGeometry, jointMaterial);
  joint.position.y = 1.0;
  joint.castShadow = true;

  shoulderGroup.add(pillar);
  shoulderGroup.add(joint);
  shoulderGroup.position.y = 0.3;

  robotParts[0].add(shoulderGroup);
  robotParts.push(shoulderGroup);
}

// 创建上臂
function createUpperArm() {
  const upperArmGroup = new THREE.Group();

  // 上臂主体 - 弯曲的圆柱体
  const armGeometry = new THREE.CylinderGeometry(0.12, 0.12, 1.5, 12);
  const armMaterial = new THREE.MeshPhongMaterial({ color: 0x2ecc71 });
  const arm = new THREE.Mesh(armGeometry, armMaterial);
  arm.position.set(0, 0.75, 0);
  arm.rotation.z = Math.PI * 0.1; // 轻微弯曲
  arm.castShadow = true;

  // 上臂关节连接器
  const connectorGeometry = new THREE.CylinderGeometry(0.18, 0.18, 0.3, 12);
  const connectorMaterial = new THREE.MeshPhongMaterial({ color: 0x34495e });
  const connector = new THREE.Mesh(connectorGeometry, connectorMaterial);
  connector.position.y = 0.15;
  connector.castShadow = true;

  upperArmGroup.add(connector);
  upperArmGroup.add(arm);
  upperArmGroup.position.y = 1.0;

  robotParts[1].add(upperArmGroup);
  robotParts.push(upperArmGroup);
}

// 创建肘部关节
function createElbowJoint() {
  const elbowGroup = new THREE.Group();

  // 肘部关节
  const elbowGeometry = new THREE.SphereGeometry(0.15, 16, 16);
  const elbowMaterial = new THREE.MeshPhongMaterial({ color: 0xf39c12 });
  const elbow = new THREE.Mesh(elbowGeometry, elbowMaterial);
  elbow.castShadow = true;

  elbowGroup.add(elbow);
  elbowGroup.position.y = 1.5;

  robotParts[2].add(elbowGroup);
  robotParts.push(elbowGroup);
}

// 创建前臂
function createForearm() {
  const forearmGroup = new THREE.Group();

  // 前臂主体
  const forearmGeometry = new THREE.CylinderGeometry(0.1, 0.1, 1.2, 12);
  const forearmMaterial = new THREE.MeshPhongMaterial({ color: 0x9b59b6 });
  const forearm = new THREE.Mesh(forearmGeometry, forearmMaterial);
  forearm.position.y = 0.6;
  forearm.castShadow = true;

  // 前臂装饰环
  const ringGeometry = new THREE.TorusGeometry(0.12, 0.02, 8, 16);
  const ringMaterial = new THREE.MeshPhongMaterial({ color: 0x7f8c8d });
  const ring = new THREE.Mesh(ringGeometry, ringMaterial);
  ring.position.y = 0.3;
  ring.rotation.x = Math.PI / 2;
  ring.castShadow = true;

  forearmGroup.add(forearm);
  forearmGroup.add(ring);
  forearmGroup.position.y = 0.15;

  robotParts[3].add(forearmGroup);
  robotParts.push(forearmGroup);
}

// 创建腕部
function createWrist() {
  const wristGroup = new THREE.Group();

  // 腕部关节
  const wristJointGeometry = new THREE.CylinderGeometry(0.08, 0.08, 0.3, 12);
  const wristJointMaterial = new THREE.MeshPhongMaterial({ color: 0x95a5a6 });
  const wristJoint = new THREE.Mesh(wristJointGeometry, wristJointMaterial);
  wristJoint.position.y = 0.15;
  wristJoint.castShadow = true;

  // 末端执行器基座
  const endEffectorGeometry = new THREE.CylinderGeometry(0.06, 0.08, 0.2, 8);
  const endEffectorMaterial = new THREE.MeshPhongMaterial({ color: 0x34495e });
  const endEffector = new THREE.Mesh(endEffectorGeometry, endEffectorMaterial);
  endEffector.position.y = 0.4;
  endEffector.castShadow = true;

  // 简单的夹爪
  const gripperGeometry = new THREE.BoxGeometry(0.03, 0.15, 0.08);
  const gripperMaterial = new THREE.MeshPhongMaterial({ color: 0xe67e22 });

  const gripper1 = new THREE.Mesh(gripperGeometry, gripperMaterial);
  gripper1.position.set(0.05, 0.55, 0);
  gripper1.castShadow = true;

  const gripper2 = new THREE.Mesh(gripperGeometry, gripperMaterial);
  gripper2.position.set(-0.05, 0.55, 0);
  gripper2.castShadow = true;

  wristGroup.add(wristJoint);
  wristGroup.add(endEffector);
  wristGroup.add(gripper1);
  wristGroup.add(gripper2);
  wristGroup.position.y = 1.2;

  robotParts[4].add(wristGroup);
  robotParts.push(wristGroup);
}

// 更新关节角度
function updateJoint(jointIndex, angle) {
  if (robotParts[jointIndex]) {
    const radians = (angle * Math.PI) / 180;

    switch (jointIndex) {
      case 0: // 基座旋转
        robotParts[jointIndex].rotation.y = radians;
        break;
      case 1: // 肩部
        robotParts[jointIndex].rotation.z = radians;
        break;
      case 2: // 肘部
        robotParts[jointIndex].rotation.z = radians;
        break;
      case 3: // 腕部旋转
        robotParts[jointIndex].rotation.y = radians;
        break;
      case 4: // 腕部俯仰
        robotParts[jointIndex].rotation.z = radians;
        break;
      case 5: // 末端执行器
        robotParts[jointIndex].rotation.y = radians;
        break;
    }
  }
}

// 重置机械臂姿态
function resetPose() {
  joints.value.forEach((joint, index) => {
    joint.angle = 0;
    updateJoint(index, 0);
  });
}

// 渲染循环
function animate() {
  requestAnimationFrame(animate);
  controls.update();
  renderer.render(scene, camera);
}

// 处理窗口大小变化
function handleResize() {
  const container = document.getElementById("robot-viewer");
  if (container) {
    const width = container.clientWidth;
    const height = container.clientHeight;

    camera.aspect = width / height;
    camera.updateProjectionMatrix();
    renderer.setSize(width, height);
  }
}

onMounted(() => {
  initScene();

  const container = document.getElementById("robot-viewer");
  if (container) {
    container.appendChild(renderer.domElement);
    handleResize();
  }

  animate();

  window.addEventListener("resize", handleResize);
});
</script>

<style scoped>
.robot-container {
  display: flex;
  height: 100vh;
  font-family: Arial, sans-serif;
}

.control-panel {
  width: 300px;
  padding: 20px;
  background-color: #f8f9fa;
  border-right: 1px solid #dee2e6;
  overflow-y: auto;
}

.control-panel h3 {
  margin-top: 0;
  color: #333;
  text-align: center;
}

.joint-control {
  margin-bottom: 20px;
}

.joint-control label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #555;
}

.slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #ddd;
  outline: none;
  -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
  border: none;
}

.reset-btn {
  width: 100%;
  padding: 10px;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  margin-top: 20px;
}

.reset-btn:hover {
  background-color: #c0392b;
}

.robot-viewer {
  flex: 1;
  position: relative;
}

.robot-viewer canvas {
  display: block;
}
</style>
