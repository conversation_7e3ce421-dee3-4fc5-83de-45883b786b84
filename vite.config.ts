import vue from '@vitejs/plugin-vue';

import { ConfigEnv, loadEnv, UserConfig } from 'vite';

const root = process.cwd()
// https://vitejs.dev/config/
export default ({ command, mode }: ConfigEnv): UserConfig => {
  let env = {} as any
  const isBuild = command === 'build'
  if (!isBuild) {
    env = loadEnv(process.argv[3] === '--mode' ? process.argv[4] : process.argv[3], root)
  } else {
    env = loadEnv(mode, root)
  }
  return {
    base: env.VITE_BASE_PATH,
    build: {
      outDir: env.VITE_OUT_DIR || 'dist', // 将生成的资源文件名

    },
    server: {
      host: '0.0.0.0', // 这个用于启动
      port: 8090, // 指定启动端口
      open: true, //启动后是否自动打开浏览器
      proxy: {
        // 选项写法
        '/api': {
          target: env.VITE_API_BASE_PATH,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      },
    },
    plugins: [
      vue(),

    ],
    resolve: {
      alias: {
        '@': '/src',
      },
    },
    // 核心代码
    // css: {
    //   preprocessorOptions: {
    //     less: {
    //       // modifyVars: {
    //       //   hack: 'true; @import "@/assets/css/less.less"'
    //       // },
    //       javascriptEnabled: true,
    //       additionalData: `@import "@/assets/css/less.less";`
    //     }
    //   }
    // }
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: {
            // 引入的每个全局样式文件后面都要加上;
            hack: 'true; @import "@/assets/css/less.less"; @import "@/style.less";'
          },
          javascriptEnabled: true,
          additionalData: `@import "@/assets/css/less.less"; @import "@/style.less";`
        },
      },
    },
  }
};