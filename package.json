{"name": "ur7e", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "pnpm vite --mode base", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"three": "^0.178.0", "urdf-loader": "^0.12.5", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}}